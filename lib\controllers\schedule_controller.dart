import 'package:get/get.dart';
import '../models/showtime_model.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/screen_model.dart';
import '../services/schedule_service.dart';
import '../services/showtime_service.dart';
import '../services/theater_service.dart';
import '../services/screen_service.dart';
import '../services/firebase_movie_service.dart';

class ScheduleController extends GetxController {
  final ScheduleService _scheduleService = ScheduleService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final TheaterService _theaterService = TheaterService();
  final ScreenService _screenService = ScreenService();
  final FirebaseMovieService _movieService = FirebaseMovieService();

  // Observable variables
  final RxList<TheaterModel> theaters = <TheaterModel>[].obs;
  final RxList<ScreenModel> screens = <ScreenModel>[].obs;
  final RxList<Movie> movies = <Movie>[].obs;
  final RxMap<String, List<ShowtimeModel>> scheduleByScreen =
      <String, List<ShowtimeModel>>{}.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Selected values for scheduling
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null);
  final Rx<TheaterModel?> selectedTheater = Rx<TheaterModel?>(null);
  final RxList<String> selectedScreenIds = <String>[].obs;
  final RxList<String> selectedDates = <String>[].obs;
  final RxList<String> selectedTimes = <String>[].obs;
  final Rx<DateTime> selectedDate = DateTime.now().obs;

  // Conflict detection
  final RxList<ScheduleConflict> conflicts = <ScheduleConflict>[].obs;
  final RxList<String> suggestedTimes = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  Future<void> loadInitialData() async {
    await Future.wait([
      loadTheaters(),
      loadMovies(),
    ]);
  }

  Future<void> loadTheaters() async {
    try {
      isLoading.value = true;
      print('ScheduleController: Loading theaters...');
      final theaterList = await _theaterService.getAllTheaters();
      print('ScheduleController: Loaded ${theaterList.length} theaters');
      theaters.value = theaterList;
      for (final theater in theaterList) {
        print('ScheduleController: Theater - ${theater.name} (${theater.id})');
      }
    } catch (e) {
      print('ScheduleController: Error loading theaters: $e');
      errorMessage.value = 'Không thể tải danh sách rạp: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadMovies() async {
    try {
      isLoading.value = true;
      print('ScheduleController: Loading movies...');
      final movieList = await _movieService.getMovies();
      print('ScheduleController: Loaded ${movieList.length} movies');
      movies.value = movieList;
      for (final movie in movieList.take(3)) {
        print('ScheduleController: Movie - ${movie.title} (${movie.id})');
      }
    } catch (e) {
      print('ScheduleController: Error loading movies: $e');
      errorMessage.value = 'Không thể tải danh sách phim: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadScreensForTheater(String theaterId) async {
    try {
      isLoading.value = true;
      print('ScheduleController: Loading screens for theater: $theaterId');
      final screenList = await _screenService.getScreensByTheater(theaterId,
          activeOnly: false);
      print('ScheduleController: Loaded ${screenList.length} screens');
      screens.value = screenList;
      for (final screen in screenList) {
        print(
            'ScheduleController: Screen - ${screen.name} (${screen.id}) - Active: ${screen.isActive}');
      }
    } catch (e) {
      print('ScheduleController: Error loading screens: $e');
      errorMessage.value = 'Không thể tải danh sách phòng chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadScheduleForDate(String theaterId, String date) async {
    try {
      isLoading.value = true;
      final schedule =
          await _scheduleService.getTheaterScheduleForDate(theaterId, date);
      scheduleByScreen.value = schedule;
    } catch (e) {
      errorMessage.value = 'Không thể tải lịch chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Set selected movie
  void setSelectedMovie(Movie movie) {
    selectedMovie.value = movie;
    _clearConflicts();
  }

  // Set selected theater
  void setSelectedTheater(TheaterModel theater) {
    print(
        'ScheduleController: Setting selected theater: ${theater.name} (${theater.id})');
    selectedTheater.value = theater;
    loadScreensForTheater(theater.id);
    selectedScreenIds.clear();
    _clearConflicts();
  }

  // Toggle screen selection
  void toggleScreenSelection(String screenId) {
    if (selectedScreenIds.contains(screenId)) {
      selectedScreenIds.remove(screenId);
    } else {
      selectedScreenIds.add(screenId);
    }
    _clearConflicts();
  }

  // Add date to selection
  void addSelectedDate(DateTime date) {
    final dateString = _formatDate(date);
    if (!selectedDates.contains(dateString)) {
      selectedDates.add(dateString);
    }
    _clearConflicts();
  }

  // Remove date from selection
  void removeSelectedDate(String date) {
    selectedDates.remove(date);
    _clearConflicts();
  }

  // Add time to selection
  void addSelectedTime(String time) {
    if (!selectedTimes.contains(time)) {
      selectedTimes.add(time);
    }
    _clearConflicts();
  }

  // Remove time from selection
  void removeSelectedTime(String time) {
    selectedTimes.remove(time);
    _clearConflicts();
  }

  // Check for conflicts before creating schedule
  Future<void> checkScheduleConflicts() async {
    if (!_isValidSelection()) {
      return;
    }

    try {
      isLoading.value = true;
      conflicts.clear();

      final movieDuration =
          _scheduleService.getMovieDuration(selectedMovie.value!);

      final conflictList = await _scheduleService.bulkCreateSchedule(
        movieId: selectedMovie.value!.id,
        theaterId: selectedTheater.value!.id,
        screenIds: selectedScreenIds.toList(),
        dates: selectedDates.toList(),
        times: selectedTimes.toList(),
        movieDuration: movieDuration,
      );

      conflicts.value = conflictList;

      if (conflicts.isNotEmpty) {
        // Collect all suggested times
        Set<String> allSuggestions = {};
        for (final conflict in conflicts) {
          allSuggestions.addAll(conflict.suggestedTimes);
        }
        suggestedTimes.value = allSuggestions.toList();
      }
    } catch (e) {
      errorMessage.value = 'Không thể kiểm tra xung đột lịch chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Create schedule (after conflict resolution)
  Future<bool> createSchedule() async {
    if (!_isValidSelection() || conflicts.isNotEmpty) {
      return false;
    }

    try {
      isLoading.value = true;

      final movieDuration =
          _scheduleService.getMovieDuration(selectedMovie.value!);

      for (final screenId in selectedScreenIds) {
        for (final date in selectedDates) {
          for (final time in selectedTimes) {
            final endTime =
                _scheduleService.calculateEndTime(time, movieDuration);

            final showtime = ShowtimeModel(
              id: '', // Will be set by Firestore
              movieId: selectedMovie.value!.id,
              theaterId: selectedTheater.value!.id,
              screenId: screenId,
              date: date,
              time: time,
              endTime: endTime,
              pricing: _getDefaultPricing(),
              availableSeats: _getScreenCapacity(screenId),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _showtimeService.createShowtime(showtime);
          }
        }
      }

      // Clear selections after successful creation
      _clearSelections();

      Get.snackbar(
        'Thành công',
        'Đã tạo lịch chiếu thành công',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Không thể tạo lịch chiếu: $e';
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Get available time slots for a specific date and screen
  Future<List<String>> getAvailableTimeSlots(
      String screenId, String date) async {
    if (selectedMovie.value == null || selectedTheater.value == null) {
      return [];
    }

    try {
      final movieDuration =
          _scheduleService.getMovieDuration(selectedMovie.value!);

      return await _scheduleService.getAvailableTimeSlots(
        theaterId: selectedTheater.value!.id,
        screenId: screenId,
        date: date,
        movieDuration: movieDuration,
      );
    } catch (e) {
      errorMessage.value = 'Không thể tải khung giờ trống: $e';
      return [];
    }
  }

  // Get popular time slots
  Future<List<String>> getPopularTimeSlots() async {
    return await _scheduleService.getPopularTimeSlots();
  }

  // Helper methods
  bool _isValidSelection() {
    return selectedMovie.value != null &&
        selectedTheater.value != null &&
        selectedScreenIds.isNotEmpty &&
        selectedDates.isNotEmpty &&
        selectedTimes.isNotEmpty;
  }

  void _clearConflicts() {
    conflicts.clear();
    suggestedTimes.clear();
  }

  void _clearSelections() {
    selectedMovie.value = null;
    selectedTheater.value = null;
    selectedScreenIds.clear();
    selectedDates.clear();
    selectedTimes.clear();
    screens.clear();
    scheduleByScreen.clear();
    _clearConflicts();
  }

  ShowtimePricing _getDefaultPricing() {
    return ShowtimePricing(
      standard: 80000,
      vip: 120000,
      couple: 150000,
      student: 60000,
      senior: 60000,
    );
  }

  int _getScreenCapacity(String screenId) {
    final screen = screens.firstWhereOrNull((s) => s.id == screenId);
    return screen?.totalSeats ?? 100;
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Validation methods
  bool get hasConflicts => conflicts.isNotEmpty;
  bool get canCreateSchedule => _isValidSelection() && !hasConflicts;

  String get selectionSummary {
    if (!_isValidSelection()) return 'Chưa chọn đủ thông tin';

    return 'Phim: ${selectedMovie.value!.title}\n'
        'Rạp: ${selectedTheater.value!.name}\n'
        'Phòng: ${selectedScreenIds.length} phòng\n'
        'Ngày: ${selectedDates.length} ngày\n'
        'Giờ: ${selectedTimes.length} suất chiếu';
  }

  // Get schedule statistics
  Map<String, int> get scheduleStats {
    final totalShowtimes =
        selectedScreenIds.length * selectedDates.length * selectedTimes.length;

    return {
      'totalShowtimes': totalShowtimes,
      'conflictCount': conflicts.length,
      'successCount': totalShowtimes - conflicts.length,
    };
  }
}
