import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/movie_model.dart';

class FirebaseMovieService {
  static const String _collection = 'movies';
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  void _log(String message) {
    developer.log(message, name: 'FirebaseMovieService');
  }

  // Get all movies
  Future<List<Movie>> getMovies() async {
    try {
      _log('Getting all movies');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      _log('Retrieved ${snapshot.docs.length} movies');
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          _log('Error parsing movie from document ${doc.id}: $e');
          // Skip this document and continue with others
        }
      }

      // Sort by createdAt manually since we can't use orderBy with where clauses without index
      movies.sort((a, b) => (b.createdAt ?? DateTime.now())
          .compareTo(a.createdAt ?? DateTime.now()));

      return movies;
    } catch (e) {
      _log('Error getting movies: $e');
      throw Exception('Failed to get movies: $e');
    }
  }

  // Get movies by status
  Future<List<Movie>> getMoviesByStatus(MovieStatus status) async {
    try {
      _log('Getting movies by status: ${status.name}');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true)
          .get();

      _log(
          'Retrieved ${snapshot.docs.length} movies with status ${status.name}');
      return snapshot.docs.map((doc) => Movie.fromFirestore(doc)).toList();
    } catch (e) {
      _log('Error getting movies by status: $e');
      throw Exception('Failed to get movies by status: $e');
    }
  }

  // Get home banner movies
  Future<List<Movie>> getHomeBannerMovies() async {
    try {
      _log('Getting home banner movies');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('isHomeBanner', isEqualTo: true)
          .get();

      _log('Retrieved ${snapshot.docs.length} home banner movies');
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          _log('Error parsing home banner movie from document ${doc.id}: $e');
          // Skip this document and continue with others
        }
      }

      // Sort by bannerOrder manually
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));

      return movies;
    } catch (e) {
      _log('Error getting home banner movies: $e');
      throw Exception('Failed to get home banner movies: $e');
    }
  }

  // Get splash banner movies
  Future<List<Movie>> getSplashBannerMovies() async {
    try {
      _log('Getting splash banner movies');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('isSplashBanner', isEqualTo: true)
          .get();

      _log('Retrieved ${snapshot.docs.length} splash banner movies');
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          _log('Error parsing splash banner movie from document ${doc.id}: $e');
          // Skip this document and continue with others
        }
      }

      // Sort by bannerOrder manually
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));

      return movies;
    } catch (e) {
      _log('Error getting splash banner movies: $e');
      throw Exception('Failed to get splash banner movies: $e');
    }
  }

  // Get movie by ID
  Future<Movie?> getMovieById(String movieId) async {
    try {
      _log('Getting movie by ID: $movieId');
      final doc = await _firestore.collection(_collection).doc(movieId).get();

      if (doc.exists) {
        return Movie.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      _log('Error getting movie by ID: $e');
      throw Exception('Failed to get movie: $e');
    }
  }

  // Search movies
  Future<List<Movie>> searchMovies(String query) async {
    try {
      _log('Searching movies with query: $query');
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final allMovies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          allMovies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }

      final movies = allMovies
          .where((movie) =>
              movie.title.toLowerCase().contains(query.toLowerCase()) ||
              (movie.originalTitle
                      ?.toLowerCase()
                      .contains(query.toLowerCase()) ??
                  false) ||
              movie.genres.any(
                  (genre) => genre.toLowerCase().contains(query.toLowerCase())))
          .toList();

      _log('Found ${movies.length} movies matching query');
      return movies;
    } catch (e) {
      _log('Error searching movies: $e');
      throw Exception('Failed to search movies: $e');
    }
  }

  // Add movie
  Future<Movie> addMovie(Movie movie) async {
    try {
      _log('Adding new movie: ${movie.title}');
      final now = DateTime.now();

      // Generate a unique ID for the movie
      final movieId = DateTime.now().millisecondsSinceEpoch;

      final movieData = movie.copyWith(
        id: movieId,
        createdAt: now,
        updatedAt: now,
      );

      final docRef =
          await _firestore.collection(_collection).add(movieData.toFirestore());

      _log(
          'Movie added with Firestore ID: ${docRef.id} and Movie ID: $movieId');
      return movieData;
    } catch (e) {
      _log('Error adding movie: $e');
      throw Exception('Failed to add movie: $e');
    }
  }

  // Update movie
  Future<void> updateMovie(Movie movie) async {
    try {
      _log('Updating movie: ${movie.title}');
      final updatedMovie = movie.copyWith(updatedAt: DateTime.now());

      // Find document by movie ID
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movie.id)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update(updatedMovie.toFirestore());
        _log('Movie updated successfully');
      } else {
        throw Exception('Movie not found with ID: ${movie.id}');
      }
    } catch (e) {
      _log('Error updating movie: $e');
      throw Exception('Failed to update movie: $e');
    }
  }

  // Delete movie (soft delete)
  Future<void> deleteMovie(String movieId) async {
    try {
      _log('Deleting movie: $movieId');

      // Find document by movie ID
      final movieIdInt = int.tryParse(movieId) ?? 0;
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieIdInt)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update({
          'isActive': false,
          'updatedAt': Timestamp.now(),
        });
        _log('Movie deleted successfully');
      } else {
        throw Exception('Movie not found with ID: $movieId');
      }
    } catch (e) {
      _log('Error deleting movie: $e');
      throw Exception('Failed to delete movie: $e');
    }
  }

  // Toggle banner status
  Future<void> toggleHomeBanner(String movieId, bool isHomeBanner,
      {int? order}) async {
    try {
      _log('Toggling home banner for movie: $movieId to $isHomeBanner');
      final updateData = {
        'isHomeBanner': isHomeBanner,
        'updatedAt': Timestamp.now(),
      };

      if (order != null) {
        updateData['bannerOrder'] = order;
      }

      // Find document by movie ID
      final movieIdInt = int.tryParse(movieId) ?? 0;
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieIdInt)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update(updateData);
      } else {
        throw Exception('Movie not found with ID: $movieId');
      }

      _log('Home banner status updated successfully');
    } catch (e) {
      _log('Error toggling home banner: $e');
      throw Exception('Failed to toggle home banner: $e');
    }
  }

  Future<void> toggleSplashBanner(String movieId, bool isSplashBanner,
      {int? order}) async {
    try {
      _log('Toggling splash banner for movie: $movieId to $isSplashBanner');
      final updateData = {
        'isSplashBanner': isSplashBanner,
        'updatedAt': Timestamp.now(),
      };

      if (order != null) {
        updateData['bannerOrder'] = order;
      }

      // Find document by movie ID
      final movieIdInt = int.tryParse(movieId) ?? 0;
      final snapshot = await _firestore
          .collection(_collection)
          .where('id', isEqualTo: movieIdInt)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        await snapshot.docs.first.reference.update(updateData);
      } else {
        throw Exception('Movie not found with ID: $movieId');
      }

      _log('Splash banner status updated successfully');
    } catch (e) {
      _log('Error toggling splash banner: $e');
      throw Exception('Failed to toggle splash banner: $e');
    }
  }

  // Stream for real-time updates
  Stream<List<Movie>> getMoviesStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }
      // Sort by createdAt manually
      movies.sort((a, b) => (b.createdAt ?? DateTime.now())
          .compareTo(a.createdAt ?? DateTime.now()));
      return movies;
    });
  }

  Stream<List<Movie>> getHomeBannerMoviesStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .where('isHomeBanner', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));
      return movies;
    });
  }

  Stream<List<Movie>> getSplashBannerMoviesStream() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .where('isSplashBanner', isEqualTo: true)
        .snapshots()
        .map((snapshot) {
      final movies = <Movie>[];
      for (final doc in snapshot.docs) {
        try {
          final movie = Movie.fromFirestore(doc);
          movies.add(movie);
        } catch (e) {
          // Skip invalid documents
        }
      }
      movies.sort(
          (a, b) => (a.bannerOrder ?? 999).compareTo(b.bannerOrder ?? 999));
      return movies;
    });
  }
}
