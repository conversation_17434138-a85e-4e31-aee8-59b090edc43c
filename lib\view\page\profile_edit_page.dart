import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import '../../controllers/auth_controller.dart';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({Key? key}) : super(key: key);

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  final AuthController _authController = Get.find<AuthController>();
  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  File? _selectedImage;
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Initialize the name controller with the current user's name
    _nameController.text = _authController.user?.name ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      // Hiển thị dialog để chọn nguồn ảnh
      await showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return SafeArea(
            child: Wrap(
              children: <Widget>[
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Chọn từ thư viện'),
                  onTap: () async {
                    Navigator.pop(context);
                    await _getImage(ImageSource.gallery);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_camera),
                  title: const Text('Chụp ảnh mới'),
                  onTap: () async {
                    Navigator.pop(context);
                    await _getImage(ImageSource.camera);
                  },
                ),
              ],
            ),
          );
        },
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to show image picker: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _getImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 75,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      // Ghi log lỗi
      debugPrint('Error picking image: $e');
      Get.snackbar(
        'Error',
        'Failed to pick image: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      String? photoUrl;

      // Tạo ảnh đại diện nếu đã chọn ảnh
      if (_selectedImage != null) {
        try {
          photoUrl = await _uploadImage();
          debugPrint('Photo URL after generation: $photoUrl');
        } catch (error) {
          debugPrint('Error generating avatar: $error');

          // Nếu có lỗi, sử dụng ảnh đại diện mặc định đơn giản
          final name = _nameController.text.trim();
          final encodedName = Uri.encodeComponent(name);
          photoUrl = 'https://ui-avatars.com/api/?name=$encodedName&size=200';

          debugPrint('Using simple avatar URL: $photoUrl');
        }
      } else {
        // Nếu không chọn ảnh, giữ nguyên ảnh hiện tại
        photoUrl = _authController.user?.photoUrl;
        debugPrint('Keeping current photo URL: $photoUrl');
      }

      // Cập nhật hồ sơ
      final success = await _authController.updateProfile(
        name: _nameController.text.trim(),
        photoUrl: photoUrl,
      );

      if (success) {
        Get.back();
        Get.snackbar(
          'Thành công',
          'Cập nhật hồ sơ thành công',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        setState(() {
          _errorMessage = _authController.errorMessage;
        });

        // Hiển thị thông báo lỗi
        Get.snackbar(
          'Lỗi',
          'Không thể cập nhật hồ sơ: ${_authController.errorMessage}',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Không thể cập nhật hồ sơ: $e';
      });

      // Hiển thị thông báo lỗi
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật hồ sơ: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<String?> _uploadImage() async {
    if (_selectedImage == null) return null;

    try {
      // Sử dụng UI Avatars để tạo ảnh đại diện từ tên người dùng
      final name = _nameController.text.trim();
      final encodedName = Uri.encodeComponent(name);

      // Tạo URL ảnh đại diện với màu nền ngẫu nhiên
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final avatarUrl =
          'https://ui-avatars.com/api/?name=$encodedName&size=200&background=random&cache=$timestamp';

      debugPrint('Generated avatar URL: $avatarUrl');

      // Hiển thị thông báo thành công
      Get.snackbar(
        'Thông báo',
        'Đã tạo ảnh đại diện từ tên của bạn.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      return avatarUrl;
    } catch (e) {
      debugPrint('Error in _uploadImage: $e');

      // Hiển thị thông báo lỗi
      Get.snackbar(
        'Lỗi',
        'Không thể tạo ảnh đại diện: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );

      throw Exception('Failed to generate avatar: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = _authController.user;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Edit Profile',
          style: GoogleFonts.mulish(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),

                // Profile image
                GestureDetector(
                  onTap: _pickImage,
                  child: Stack(
                    children: [
                      CircleAvatar(
                        radius: 60,
                        backgroundColor: Colors.white24,
                        backgroundImage: _selectedImage != null
                            ? FileImage(_selectedImage!) as ImageProvider
                            : (user?.photoUrl != null
                                ? NetworkImage(user!.photoUrl!)
                                : null),
                        child:
                            (_selectedImage == null && user?.photoUrl == null)
                                ? Icon(
                                    Icons.person,
                                    size: 60,
                                    color: Colors.white.withOpacity(0.7),
                                  )
                                : null,
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),

                // Display name field
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Display Name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    prefixIcon: const Icon(Icons.person),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a display name';
                    }
                    return null;
                  },
                ),

                if (_errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                const SizedBox(height: 30),

                // Save button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _updateProfile,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator()
                        : const Text(
                            'Save Changes',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
