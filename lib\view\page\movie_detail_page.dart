import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:readmore/readmore.dart';

import '../../controllers/movie_controller.dart';
import '../../controllers/favorite_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/movie_model.dart';
import '../user/movie_booking_page.dart';

class MovieDetailsPage extends StatefulWidget {
  final int? movieId;

  const MovieDetailsPage({Key? key, this.movieId}) : super(key: key);

  @override
  State<MovieDetailsPage> createState() => _MovieDetailsPageState();
}

class _MovieDetailsPageState extends State<MovieDetailsPage> {
  final MovieController _movieController = Get.find<MovieController>();
  final FavoriteController _favoriteController = Get.find<FavoriteController>();
  final AuthController _authController = Get.find<AuthController>();

  final RxBool _expanded = false.obs;
  final RxDouble _velocity = 0.0.obs;

  // Legacy movie details for backward compatibility
  final Map<String, dynamic> _legacyMovieDetails = {
    'title': 'Thor',
    'subtitle': 'The Dark World',
    'banner':
        'https://m.media-amazon.com/images/M/MV5BMTQyNzAwOTUxOF5BMl5BanBnXkFtZTcwMTE0OTc5OQ@@._V1_FMjpg_UX1000_.jpg',
    'genre': 'Action',
    'ar': '16+',
    'rating': '6.8',
    'plot':
        'When the Dark Elves attempt to plunge the universe into darkness, Thor must embark on a perilous and personal journey that will reunite him with doctor Jane Foster.',
    'casts': [
      {
        'name': 'Chris Hemsworth',
        'charecter': 'Thor',
        'image':
            'https://m.media-amazon.com/images/M/MV5BOTU2MTI0NTIyNV5BMl5BanBnXkFtZTcwMTA4Nzc3OA@@._V1_UX214_CR0,0,214,317_AL_.jpg',
      },
      {
        'name': 'Natalie Portman',
        'charecter': 'Jane Foster',
        'image':
            'https://m.media-amazon.com/images/M/MV5BOTU2MTI0NTIyNV5BMl5BanBnXkFtZTcwMTA4Nzc3OA@@._V1_UX214_CR0,0,214,317_AL_.jpg',
      },
      {
        'name': 'Tom Hiddleston',
        'charecter': 'Loki',
        'image':
            'https://m.media-amazon.com/images/M/MV5BOTU2MTI0NTIyNV5BMl5BanBnXkFtZTcwMTA4Nzc3OA@@._V1_UX214_CR0,0,214,317_AL_.jpg',
      },
    ]
  };

  @override
  void initState() {
    super.initState();
    if (widget.movieId != null) {
      _movieController.getMovieDetails(widget.movieId!);
    }
  }

  void _onDragEnd(DragEndDetails details) {
    _velocity.value = details.velocity.pixelsPerSecond.dx.abs().floorToDouble();
    if (_velocity.value > 0) {
      _expanded.value = !_expanded.value;
    }
  }

  void _toggleFavorite(Movie movie) {
    _favoriteController.toggleFavorite(movie);
  }

  void _navigateToBooking(Movie movie) {
    if (!_authController.isLoggedIn) {
      Get.snackbar(
        'Đăng nhập yêu cầu',
        'Vui lòng đăng nhập để đặt vé',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange,
        colorText: Colors.white,
      );
      Get.toNamed('/login');
      return;
    }

    Get.to(() => MovieBookingPage(movie: movie));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Obx(() {
          // If we have a movie ID and the movie is loaded, show the movie details
          if (widget.movieId != null &&
              _movieController.selectedMovie.value != null) {
            final movie = _movieController.selectedMovie.value!;
            return _buildMovieDetails(context, movie);
          }

          // Otherwise, show the legacy movie details
          return _buildLegacyMovieDetails(context);
        }),
      ),
    );
  }

  Widget _buildMovieDetails(BuildContext context, Movie movie) {
    final isFavorite = _favoriteController.isFavorite(movie.id);

    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xff2B5876),
            Color(0xff4E4376),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Movie backdrop
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            width: double.infinity,
            child: Image.network(
              movie.fullBackdropPath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 50,
                    ),
                  ),
                );
              },
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
              ),
              style: IconButton.styleFrom(
                backgroundColor: Colors.black38,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),

          // Movie details card
          Obx(
            () => Positioned(
              top: _expanded.value
                  ? 0
                  : MediaQuery.of(context).size.height * 0.4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                width: MediaQuery.of(context).size.width,
                height: _expanded.value
                    ? MediaQuery.of(context).size.height
                    : MediaQuery.of(context).size.height * .6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                    topRight: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                  ),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xff2B5876),
                      Color(0xff4E4376),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // Movie title section with drag handle
                    GestureDetector(
                      onVerticalDragEnd: _onDragEnd,
                      child: Column(
                        children: [
                          if (_expanded.value) const SizedBox(height: 25),
                          Text(
                            movie.title,
                            style: GoogleFonts.mulish(
                              fontSize: 40,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          if (movie.subtitle != null)
                            Text(
                              movie.subtitle!,
                              style: GoogleFonts.mulish(
                                fontSize: 18,
                                fontWeight: FontWeight.w700,
                                color: Colors.grey,
                              ),
                            ),
                          const SizedBox(height: 15),

                          // Movie info chips
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Genre, age rating, and IMDB rating
                              Expanded(
                                child: SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    children: [
                                      if (movie.genres.isNotEmpty)
                                        Chip(
                                          backgroundColor: Colors.blueGrey[300],
                                          label: Text(
                                            movie.genres.first,
                                            style: GoogleFonts.mulish(
                                              fontSize: 12,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                      const SizedBox(width: 5),
                                      if (movie.ageRating != null)
                                        Chip(
                                          backgroundColor: Colors.blueGrey[300],
                                          label: Text(
                                            movie.ageRating!,
                                            style: GoogleFonts.mulish(
                                              fontSize: 12,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                        ),
                                      const SizedBox(width: 5),
                                      Chip(
                                        backgroundColor: Colors.amber,
                                        label: Text(
                                          'IMDB ${movie.rating}',
                                          style: GoogleFonts.mulish(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // Share and favorite buttons
                              Row(
                                children: [
                                  IconButton(
                                    onPressed: () {
                                      Get.toNamed('/share', arguments: movie);
                                    },
                                    icon: Icon(
                                      Icons.share,
                                      color: Colors.blueGrey[100],
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () => _toggleFavorite(movie),
                                    icon: Icon(
                                      isFavorite
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      color: isFavorite
                                          ? Colors.red
                                          : Colors.blueGrey[100],
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                          const SizedBox(height: 15),
                        ],
                      ),
                    ),

                    // Scrollable content (plot and cast)
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Plot
                            if (movie.overview != null)
                              ReadMoreText(
                                movie.overview!,
                                trimCollapsedText: 'More',
                                moreStyle: TextStyle(
                                  color: Colors.blue[300],
                                ),
                                lessStyle: TextStyle(
                                  color: Colors.red[200],
                                ),
                                trimExpandedText: 'Less',
                                textAlign: TextAlign.justify,
                                trimLines: 3,
                                trimMode: TrimMode.Line,
                                style: GoogleFonts.mulish(
                                  color: Colors.white,
                                  height: 1.5,
                                ),
                              ),
                            const SizedBox(height: 15),

                            // Cast section
                            if (movie.cast != null &&
                                movie.cast!.isNotEmpty) ...[
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Cast',
                                    style: GoogleFonts.mulish(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15),
                              SizedBox(
                                height: 150,
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: movie.cast!.length,
                                  itemBuilder: (context, index) {
                                    final cast = movie.cast![index];
                                    return Container(
                                      width: 80,
                                      margin: const EdgeInsets.only(right: 10),
                                      child: Column(
                                        children: [
                                          ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(15),
                                            child: Image.network(
                                              cast.fullProfilePath,
                                              width: 70,
                                              height: 70,
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (context, error, stackTrace) {
                                                return Container(
                                                  width: 70,
                                                  height: 70,
                                                  color: Colors.grey[800],
                                                  child: const Icon(
                                                    Icons.person,
                                                    color: Colors.white54,
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                          const SizedBox(height: 5),
                                          Text(
                                            cast.name,
                                            textAlign: TextAlign.center,
                                            style: GoogleFonts.mulish(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 5),
                                          Text(
                                            cast.character,
                                            textAlign: TextAlign.center,
                                            style: GoogleFonts.mulish(
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white54,
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(height: 30),
                            ],

                            // Book Ticket Button
                            Container(
                              width: double.infinity,
                              height: 55,
                              margin: const EdgeInsets.symmetric(vertical: 20),
                              child: ElevatedButton(
                                onPressed: () => _navigateToBooking(movie),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.amber,
                                  foregroundColor: Colors.black,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  elevation: 5,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.movie_creation_outlined,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      'Đặt vé ngay',
                                      style: GoogleFonts.mulish(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Legacy implementation for backward compatibility
  Widget _buildLegacyMovieDetails(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xff2B5876),
            Color(0xff4E4376),
          ],
        ),
      ),
      child: Stack(
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            width: double.infinity,
            child: Image.network(
              _legacyMovieDetails['banner'].toString(),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.white54,
                      size: 50,
                    ),
                  ),
                );
              },
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () {
                Get.back();
              },
              icon: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
              ),
              style: IconButton.styleFrom(
                backgroundColor: Colors.black38,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),

          Obx(
            () => Positioned(
              top: _expanded.value
                  ? 0
                  : MediaQuery.of(context).size.height * 0.4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                width: MediaQuery.of(context).size.width,
                height: _expanded.value
                    ? MediaQuery.of(context).size.height
                    : MediaQuery.of(context).size.height * .6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                    topRight: _expanded.value == true
                        ? Radius.zero
                        : const Radius.circular(50),
                  ),
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xff2B5876),
                      Color(0xff4E4376),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    GestureDetector(
                      onVerticalDragEnd: _onDragEnd,
                      child: Column(
                        children: [
                          if (_expanded.value) const SizedBox(height: 25),
                          Text(
                            _legacyMovieDetails['title'].toString(),
                            style: GoogleFonts.mulish(
                              fontSize: 40,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            _legacyMovieDetails['subtitle'].toString(),
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 15),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Chip(
                                    backgroundColor: Colors.blueGrey[300],
                                    label: Text(
                                      _legacyMovieDetails['genre'].toString(),
                                      style: GoogleFonts.mulish(
                                        fontSize: 12,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Chip(
                                    backgroundColor: Colors.blueGrey[300],
                                    label: Text(
                                      _legacyMovieDetails['ar'].toString(),
                                      style: GoogleFonts.mulish(
                                        fontSize: 12,
                                        color: Colors.white,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 5),
                                  Chip(
                                    backgroundColor: Colors.amber,
                                    label: Text(
                                      'IMDB ${_legacyMovieDetails['rating']}',
                                      style: GoogleFonts.mulish(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  IconButton(
                                    onPressed: () {},
                                    icon: Icon(
                                      Icons.share,
                                      color: Colors.blueGrey[100],
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () {},
                                    icon: Icon(
                                      Icons.favorite_border,
                                      color: Colors.blueGrey[100],
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                          const SizedBox(height: 15),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ReadMoreText(
                              _legacyMovieDetails['plot'].toString(),
                              trimCollapsedText: 'More',
                              moreStyle: TextStyle(
                                color: Colors.blue[300],
                              ),
                              lessStyle: TextStyle(
                                color: Colors.red[200],
                              ),
                              trimExpandedText: 'Less',
                              textAlign: TextAlign.justify,
                              trimLines: 3,
                              trimMode: TrimMode.Line,
                              style: GoogleFonts.mulish(
                                color: Colors.white,
                                height: 1.5,
                              ),
                            ),
                            const SizedBox(height: 15),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Cast',
                                  style: GoogleFonts.mulish(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 15),
                            SizedBox(
                              height: 150,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount:
                                    (_legacyMovieDetails['casts'] as List)
                                        .length,
                                itemBuilder: (context, index) {
                                  final cast = (_legacyMovieDetails['casts']
                                      as List)[index];
                                  return Container(
                                    width: 80,
                                    margin: const EdgeInsets.only(right: 10),
                                    child: Column(
                                      children: [
                                        ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(15),
                                          child: Image.network(
                                            cast['image'],
                                            width: 70,
                                            height: 70,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        const SizedBox(height: 5),
                                        Text(
                                          cast['name'],
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.mulish(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 5),
                                        Text(
                                          cast['charecter'],
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.mulish(
                                            fontSize: 10,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white54,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 30),

                            // Book Ticket Button for Legacy Movie
                            Container(
                              width: double.infinity,
                              height: 55,
                              margin: const EdgeInsets.symmetric(vertical: 20),
                              child: ElevatedButton(
                                onPressed: () {
                                  Get.snackbar(
                                    'Thông báo',
                                    'Chức năng đặt vé chỉ khả dụng cho phim từ cơ sở dữ liệu',
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor: Colors.orange,
                                    colorText: Colors.white,
                                  );
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.grey[600],
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  elevation: 5,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.movie_creation_outlined,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      'Đặt vé (Không khả dụng)',
                                      style: GoogleFonts.mulish(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
