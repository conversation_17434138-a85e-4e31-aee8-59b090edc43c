import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../models/ticket_model.dart';
import '../../services/ticket_service.dart';

class TicketManagementPage extends StatefulWidget {
  const TicketManagementPage({Key? key}) : super(key: key);

  @override
  State<TicketManagementPage> createState() => _TicketManagementPageState();
}

class _TicketManagementPageState extends State<TicketManagementPage> {
  final TicketService _ticketService = TicketService();
  final RxList<Ticket> _tickets = <Ticket>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _selectedStatus = 'all'.obs;
  final RxString _searchQuery = ''.obs;

  @override
  void initState() {
    super.initState();
    _loadTickets();
  }

  Future<void> _loadTickets() async {
    try {
      _isLoading.value = true;

      // Get all tickets from Firestore directly for admin
      final snapshot = await FirebaseFirestore.instance
          .collection('tickets')
          .orderBy('purchaseDate', descending: true)
          .get();

      final tickets =
          snapshot.docs.map((doc) => Ticket.fromFirestore(doc)).toList();

      _tickets.value = tickets;
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách vé: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  List<Ticket> get _filteredTickets {
    var tickets = _tickets.toList();

    // Filter by status
    if (_selectedStatus.value != 'all') {
      final status = TicketStatusExtension.fromString(_selectedStatus.value);
      tickets = tickets.where((ticket) => ticket.status == status).toList();
    }

    // Filter by search query
    if (_searchQuery.value.isNotEmpty) {
      final query = _searchQuery.value.toLowerCase();
      tickets = tickets
          .where((ticket) =>
              ticket.movieTitle.toLowerCase().contains(query) ||
              ticket.bookingCode.toLowerCase().contains(query) ||
              ticket.theaterName.toLowerCase().contains(query))
          .toList();
    }

    return tickets;
  }

  Future<void> _updateTicketStatus(
      Ticket ticket, TicketStatus newStatus) async {
    try {
      await _ticketService.updateTicketStatus(ticket.id, newStatus);
      await _loadTickets(); // Refresh list

      Get.snackbar(
        'Thành công',
        'Đã cập nhật trạng thái vé',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.7),
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật trạng thái vé: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản lý vé',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _loadTickets,
                      icon: const Icon(Icons.refresh, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Filters
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Search bar
                    TextField(
                      onChanged: (value) => _searchQuery.value = value,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        hintText: 'Tìm kiếm theo tên phim, mã vé, rạp...',
                        hintStyle:
                            TextStyle(color: Colors.white.withOpacity(0.7)),
                        prefixIcon:
                            const Icon(Icons.search, color: Colors.white),
                        filled: true,
                        fillColor: Colors.white.withOpacity(0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Status filter
                    Obx(() => SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildStatusChip('all', 'Tất cả'),
                              const SizedBox(width: 8),
                              _buildStatusChip('confirmed', 'Đã xác nhận'),
                              const SizedBox(width: 8),
                              _buildStatusChip('used', 'Đã sử dụng'),
                              const SizedBox(width: 8),
                              _buildStatusChip('cancelled', 'Đã hủy'),
                            ],
                          ),
                        )),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Tickets list
              Expanded(
                child: Obx(() {
                  if (_isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  final filteredTickets = _filteredTickets;

                  if (filteredTickets.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.confirmation_number_outlined,
                            size: 64,
                            color: Colors.white.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Không có vé nào',
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredTickets.length,
                    itemBuilder: (context, index) {
                      final ticket = filteredTickets[index];
                      return _buildTicketCard(ticket);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String value, String label) {
    final isSelected = _selectedStatus.value == value;
    return GestureDetector(
      onTap: () => _selectedStatus.value = value,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white.withOpacity(0.3),
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.mulish(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.8),
          ),
        ),
      ),
    );
  }

  Widget _buildTicketCard(Ticket ticket) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with movie title and status
          Row(
            children: [
              Expanded(
                child: Text(
                  ticket.movieTitle,
                  style: GoogleFonts.mulish(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              _buildStatusBadge(ticket.status),
            ],
          ),
          const SizedBox(height: 8),

          // Booking details
          Text(
            'Mã vé: ${ticket.bookingCode}',
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.blue[200],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${ticket.theaterName} • ${ticket.screenName}',
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${ticket.date} • ${ticket.time}',
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Ghế: ${ticket.seats.map((s) => '${s.row}${s.number}').join(', ')}',
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),

          // Price and actions
          Row(
            children: [
              Expanded(
                child: Text(
                  '${ticket.finalPrice.toStringAsFixed(0)} VNĐ',
                  style: GoogleFonts.mulish(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber,
                  ),
                ),
              ),
              if (ticket.status == TicketStatus.confirmed) ...[
                TextButton(
                  onPressed: () =>
                      _updateTicketStatus(ticket, TicketStatus.used),
                  child: Text(
                    'Đánh dấu đã dùng',
                    style: GoogleFonts.mulish(
                      fontSize: 12,
                      color: Colors.green[300],
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () =>
                      _updateTicketStatus(ticket, TicketStatus.cancelled),
                  child: Text(
                    'Hủy vé',
                    style: GoogleFonts.mulish(
                      fontSize: 12,
                      color: Colors.red[300],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(TicketStatus status) {
    Color color;
    String text;

    switch (status) {
      case TicketStatus.confirmed:
        color = Colors.green;
        text = 'Đã xác nhận';
        break;
      case TicketStatus.used:
        color = Colors.blue;
        text = 'Đã sử dụng';
        break;
      case TicketStatus.cancelled:
        color = Colors.red;
        text = 'Đã hủy';
        break;
      case TicketStatus.expired:
        color = Colors.orange;
        text = 'Đã hết hạn';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        text,
        style: GoogleFonts.mulish(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
      ),
    );
  }
}
