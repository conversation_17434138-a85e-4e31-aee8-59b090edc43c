import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/schedule_controller.dart';
import '../../models/movie_model.dart';
import '../../models/theater_model.dart';
import '../../models/screen_model.dart' as screen;
import '../../services/screen_service.dart';
// import 'schedule_calendar_page.dart'; // TODO: Create this page

class ScheduleManagementPage extends StatefulWidget {
  const ScheduleManagementPage({Key? key}) : super(key: key);

  @override
  State<ScheduleManagementPage> createState() => _ScheduleManagementPageState();
}

class _ScheduleManagementPageState extends State<ScheduleManagementPage> {
  final ScheduleController _scheduleController = Get.put(ScheduleController());
  final PageController _pageController = PageController();
  final RxInt _currentStep = 0.obs;
  final ScreenService _screenService = ScreenService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản lý lịch chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        // TODO: Implement calendar view
                        Get.snackbar(
                          'Thông báo',
                          'Tính năng xem lịch sẽ được cập nhật sớm',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      },
                      icon:
                          const Icon(Icons.calendar_month, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Step indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Obx(() => Row(
                      children: [
                        _buildStepIndicator(
                            0, 'Chọn phim', _currentStep.value >= 0),
                        _buildStepConnector(_currentStep.value >= 1),
                        _buildStepIndicator(
                            1, 'Chọn rạp', _currentStep.value >= 1),
                        _buildStepConnector(_currentStep.value >= 2),
                        _buildStepIndicator(
                            2, 'Chọn lịch', _currentStep.value >= 2),
                        _buildStepConnector(_currentStep.value >= 3),
                        _buildStepIndicator(
                            3, 'Xác nhận', _currentStep.value >= 3),
                      ],
                    )),
              ),

              const SizedBox(height: 20),

              // Content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) => _currentStep.value = index,
                  children: [
                    _buildMovieSelectionStep(),
                    _buildTheaterSelectionStep(),
                    _buildScheduleSelectionStep(),
                    _buildConfirmationStep(),
                  ],
                ),
              ),

              // Navigation buttons
              Container(
                padding: const EdgeInsets.all(16),
                child: Obx(() => Row(
                      children: [
                        if (_currentStep.value > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _previousStep,
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.white,
                                side: const BorderSide(color: Colors.white),
                              ),
                              child: Text(
                                'Quay lại',
                                style: GoogleFonts.mulish(),
                              ),
                            ),
                          ),
                        if (_currentStep.value > 0) const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _canProceed() ? _nextStep : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              _getNextButtonText(),
                              style: GoogleFonts.mulish(
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label, bool isActive) {
    return Column(
      children: [
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: isActive ? Colors.blue : Colors.white.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: GoogleFonts.mulish(
                color: isActive ? Colors.white : Colors.white.withOpacity(0.7),
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.mulish(
            color: Colors.white.withOpacity(0.8),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildStepConnector(bool isActive) {
    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.only(bottom: 20),
        color: isActive ? Colors.blue : Colors.white.withOpacity(0.3),
      ),
    );
  }

  Widget _buildMovieSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn phim cần lập lịch chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              if (_scheduleController.movies.isEmpty) {
                return Center(
                  child: Text(
                    'Không có phim nào',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              return ListView.builder(
                itemCount: _scheduleController.movies.length,
                itemBuilder: (context, index) {
                  final movie = _scheduleController.movies[index];
                  return _buildMovieCard(movie);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildMovieCard(Movie movie) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedMovie.value?.id == movie.id;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              movie.fullPosterPath,
              width: 50,
              height: 75,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 50,
                  height: 75,
                  color: Colors.grey[800],
                  child: const Icon(Icons.movie, color: Colors.white54),
                );
              },
            ),
          ),
          title: Text(
            movie.title,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (movie.runtime != null)
                Text(
                  '${movie.runtime} phút',
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
              if (movie.genres.isNotEmpty)
                Text(
                  movie.genres.take(2).join(', '),
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
            ],
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.blue)
              : null,
          onTap: () => _scheduleController.setSelectedMovie(movie),
        ),
      );
    });
  }

  Widget _buildTheaterSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn rạp chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              return ListView.builder(
                itemCount: _scheduleController.theaters.length,
                itemBuilder: (context, index) {
                  final theater = _scheduleController.theaters[index];
                  return _buildTheaterCard(theater);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTheaterCard(TheaterModel theater) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedTheater.value?.id == theater.id;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ExpansionTile(
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.local_movies, color: Colors.blue),
          ),
          title: Text(
            theater.name,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            theater.address.fullAddress,
            style: GoogleFonts.mulish(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.blue)
              : const Icon(Icons.expand_more, color: Colors.white),
          onExpansionChanged: (expanded) {
            if (expanded) {
              _scheduleController.setSelectedTheater(theater);
            }
          },
          children: [
            if (isSelected) _buildScreenSelection(),
            if (isSelected && _scheduleController.screens.isEmpty)
              _buildCreateScreensButton(),
          ],
        ),
      );
    });
  }

  Widget _buildScreenSelection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Chọn phòng chiếu:',
                style: GoogleFonts.mulish(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              GestureDetector(
                onTap: () {
                  if (_scheduleController.selectedTheater.value != null) {
                    _scheduleController.loadScreensForTheater(
                        _scheduleController.selectedTheater.value!.id);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.white.withOpacity(0.3)),
                  ),
                  child: const Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Obx(() {
            final activeScreens =
                _scheduleController.screens.where((s) => s.isActive).toList();
            final inactiveScreens =
                _scheduleController.screens.where((s) => !s.isActive).toList();

            // Debug info
            print('ScheduleManagement: Total screens: ${_scheduleController.screens.length}');
            print('ScheduleManagement: Active screens: ${activeScreens.length}');
            print('ScheduleManagement: Inactive screens: ${inactiveScreens.length}');
            for (final screen in _scheduleController.screens) {
              print('ScheduleManagement: Screen ${screen.name} - Active: ${screen.isActive}');
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Active screens
                if (activeScreens.isNotEmpty) ...[
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: activeScreens.map((screen) {
                      final isSelected = _scheduleController.selectedScreenIds
                          .contains(screen.id);
                      return GestureDetector(
                        onTap: () => _scheduleController
                            .toggleScreenSelection(screen.id),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.blue
                                : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: isSelected
                                  ? Colors.blue
                                  : Colors.white.withOpacity(0.3),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                screen.type.icon,
                                size: 14,
                                color: isSelected
                                    ? Colors.white
                                    : screen.type.color,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                screen.name,
                                style: GoogleFonts.mulish(
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.white.withOpacity(0.8),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],

                // Inactive screens (if any)
                if (inactiveScreens.isNotEmpty) ...[
                  if (activeScreens.isNotEmpty) const SizedBox(height: 12),
                  Text(
                    'Phòng chiếu tạm dừng:',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: inactiveScreens.map((screen) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                          border:
                              Border.all(color: Colors.grey.withOpacity(0.4)),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              screen.type.icon,
                              size: 14,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              screen.name,
                              style: GoogleFonts.mulish(
                                color: Colors.grey,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.block,
                              size: 12,
                              color: Colors.grey,
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ],

                // No screens message
                if (activeScreens.isEmpty && inactiveScreens.isEmpty)
                  Text(
                    'Không có phòng chiếu nào',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 12,
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _buildCreateScreensButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Text(
            'Rạp này chưa có phòng chiếu nào',
            style: GoogleFonts.mulish(
              color: Colors.white.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: () => _createDefaultScreens(),
            icon: const Icon(Icons.add),
            label: Text(
              'Tạo phòng chiếu mẫu',
              style: GoogleFonts.mulish(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn ngày và giờ chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDateSelection(),
                  const SizedBox(height: 24),
                  _buildTimeSelection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn ngày chiếu:',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 30, // Next 30 days
            itemBuilder: (context, index) {
              final date = DateTime.now().add(Duration(days: index));
              final dateString = _formatDate(date);

              return Obx(() {
                final isSelected =
                    _scheduleController.selectedDates.contains(dateString);

                return GestureDetector(
                  onTap: () {
                    if (isSelected) {
                      _scheduleController.removeSelectedDate(dateString);
                    } else {
                      _scheduleController.addSelectedDate(date);
                    }
                  },
                  child: Container(
                    width: 70,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.blue
                          : Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue
                            : Colors.white.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _getDayName(date),
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${date.day}',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _getMonthName(date),
                          style: GoogleFonts.mulish(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn giờ chiếu:',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        FutureBuilder<List<String>>(
          future: _scheduleController.getPopularTimeSlots(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const CircularProgressIndicator(color: Colors.white);
            }

            return Obx(() => Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: snapshot.data!.map((time) {
                    final isSelected =
                        _scheduleController.selectedTimes.contains(time);

                    return GestureDetector(
                      onTap: () {
                        if (isSelected) {
                          _scheduleController.removeSelectedTime(time);
                        } else {
                          _scheduleController.addSelectedTime(time);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.blue
                              : Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? Colors.blue
                                : Colors.white.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          time,
                          style: GoogleFonts.mulish(
                            color: isSelected
                                ? Colors.white
                                : Colors.white.withOpacity(0.8),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ));
          },
        ),
      ],
    );
  }

  Widget _buildConfirmationStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Xác nhận lịch chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildSummaryCard(),
                  const SizedBox(height: 16),
                  _buildConflictCheck(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tóm tắt lịch chiếu',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Obx(() => Text(
                _scheduleController.selectionSummary,
                style: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildConflictCheck() {
    return Obx(() {
      if (_scheduleController.conflicts.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withOpacity(0.5)),
          ),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Không có xung đột lịch chiếu',
                  style: GoogleFonts.mulish(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withOpacity(0.5)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 12),
                Text(
                  'Có ${_scheduleController.conflicts.length} xung đột',
                  style: GoogleFonts.mulish(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ...(_scheduleController.conflicts.take(3).map((conflict) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• ${conflict.message}',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                ))),
            if (_scheduleController.suggestedTimes.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Giờ chiếu được đề xuất:',
                style: GoogleFonts.mulish(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                children: _scheduleController.suggestedTimes
                    .take(5)
                    .map((time) => Chip(
                          label: Text(time),
                          backgroundColor: Colors.blue.withOpacity(0.3),
                          labelStyle: GoogleFonts.mulish(
                              color: Colors.white, fontSize: 10),
                        ))
                    .toList(),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Navigation methods
  void _nextStep() {
    if (_currentStep.value < 3) {
      if (_currentStep.value == 2) {
        // Check conflicts before going to confirmation
        _scheduleController.checkScheduleConflicts();
      }
      _currentStep.value++;
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Create schedule
      _scheduleController.createSchedule().then((success) {
        if (success) {
          Get.back();
        }
      });
    }
  }

  void _previousStep() {
    if (_currentStep.value > 0) {
      _currentStep.value--;
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceed() {
    final step = _currentStep.value;
    bool canProceed = false;

    switch (step) {
      case 0:
        canProceed = _scheduleController.selectedMovie.value != null;
        print(
            'ScheduleManagement: Step 0 - Movie selected: ${_scheduleController.selectedMovie.value?.title}, canProceed: $canProceed');
        break;
      case 1:
        final activeScreens =
            _scheduleController.screens.where((s) => s.isActive).length;
        canProceed = _scheduleController.selectedTheater.value != null &&
            activeScreens > 0 &&
            _scheduleController.selectedScreenIds.isNotEmpty;
        print(
            'ScheduleManagement: Step 1 - Theater: ${_scheduleController.selectedTheater.value?.name}, Total Screens: ${_scheduleController.screens.length}, Active Screens: $activeScreens, Selected Screens: ${_scheduleController.selectedScreenIds.length}, canProceed: $canProceed');
        break;
      case 2:
        canProceed = _scheduleController.selectedDates.isNotEmpty &&
            _scheduleController.selectedTimes.isNotEmpty;
        print(
            'ScheduleManagement: Step 2 - Dates: ${_scheduleController.selectedDates.length}, Times: ${_scheduleController.selectedTimes.length}, canProceed: $canProceed');
        break;
      case 3:
        canProceed = _scheduleController.canCreateSchedule;
        print(
            'ScheduleManagement: Step 3 - canCreateSchedule: $canProceed, hasConflicts: ${_scheduleController.hasConflicts}');
        break;
      default:
        canProceed = false;
    }

    return canProceed;
  }

  String _getNextButtonText() {
    switch (_currentStep.value) {
      case 0:
        return 'Chọn rạp';
      case 1:
        return 'Chọn lịch';
      case 2:
        return 'Kiểm tra';
      case 3:
        return 'Tạo lịch chiếu';
      default:
        return 'Tiếp tục';
    }
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _getDayName(DateTime date) {
    const days = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    return days[date.weekday % 7];
  }

  String _getMonthName(DateTime date) {
    const months = [
      'T1',
      'T2',
      'T3',
      'T4',
      'T5',
      'T6',
      'T7',
      'T8',
      'T9',
      'T10',
      'T11',
      'T12'
    ];
    return months[date.month - 1];
  }

  // Create default screens for theater
  Future<void> _createDefaultScreens() async {
    final theater = _scheduleController.selectedTheater.value;
    if (theater == null) return;

    try {
      // Show loading
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
        barrierDismissible: false,
      );

      // Create default screens
      final defaultScreens = [
        screen.ScreenModel(
          id: '',
          theaterId: theater.id,
          name: 'Phòng 1',
          type: screen.ScreenType.standard,
          totalSeats: 100,
          rows: 10,
          seatsPerRow: 10,
          seatLayout: _createDefaultSeatLayout(10, 10),
          amenities: ['Air Conditioning', 'Sound System'],
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        screen.ScreenModel(
          id: '',
          theaterId: theater.id,
          name: 'Phòng 2',
          type: screen.ScreenType.vip,
          totalSeats: 80,
          rows: 8,
          seatsPerRow: 10,
          seatLayout: _createDefaultSeatLayout(8, 10),
          amenities: ['Air Conditioning', 'Sound System', 'Reclining Seats'],
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        screen.ScreenModel(
          id: '',
          theaterId: theater.id,
          name: 'Phòng 3',
          type: screen.ScreenType.imax,
          totalSeats: 120,
          rows: 12,
          seatsPerRow: 10,
          seatLayout: _createDefaultSeatLayout(12, 10),
          amenities: ['Air Conditioning', 'IMAX Sound System', 'Large Screen'],
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      // Add screens to Firebase
      for (final screen in defaultScreens) {
        await _screenService.addScreen(screen);
      }

      // Close loading dialog
      Get.back();

      // Reload screens
      await _scheduleController.loadScreensForTheater(theater.id);

      // Show success message
      Get.snackbar(
        'Thành công',
        'Đã tạo ${defaultScreens.length} phòng chiếu mẫu',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      // Close loading dialog
      Get.back();

      // Show error message
      Get.snackbar(
        'Lỗi',
        'Không thể tạo phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // Create default seat layout
  List<screen.SeatRowModel> _createDefaultSeatLayout(
      int rows, int seatsPerRow) {
    final seatRows = <screen.SeatRowModel>[];

    for (int i = 0; i < rows; i++) {
      final rowLetter = String.fromCharCode(65 + i); // A, B, C, etc.
      final seats = <screen.SeatModel>[];

      for (int j = 1; j <= seatsPerRow; j++) {
        seats.add(screen.SeatModel(
          number: j.toString(),
          type: j <= 2 || j >= seatsPerRow - 1
              ? screen.SeatType.couple
              : screen.SeatType.standard,
          isAvailable: true,
        ));
      }

      seatRows.add(screen.SeatRowModel(
        row: rowLetter,
        seats: seats,
      ));
    }

    return seatRows;
  }
}
