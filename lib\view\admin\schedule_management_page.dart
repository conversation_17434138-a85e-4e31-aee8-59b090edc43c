import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/schedule_controller.dart';
import '../../models/movie_model.dart';
import '../../models/theater_model.dart';
import '../../models/screen_model.dart';
// import 'schedule_calendar_page.dart'; // TODO: Create this page

class ScheduleManagementPage extends StatefulWidget {
  const ScheduleManagementPage({Key? key}) : super(key: key);

  @override
  State<ScheduleManagementPage> createState() => _ScheduleManagementPageState();
}

class _ScheduleManagementPageState extends State<ScheduleManagementPage> {
  final ScheduleController _scheduleController = Get.put(ScheduleController());
  final PageController _pageController = PageController();
  final RxInt _currentStep = 0.obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản lý lịch chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        // TODO: Implement calendar view
                        Get.snackbar(
                          'Thông báo',
                          'Tính năng xem lịch sẽ được cập nhật sớm',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                      },
                      icon:
                          const Icon(Icons.calendar_month, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Step indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Obx(() => Row(
                      children: [
                        _buildStepIndicator(
                            0, 'Chọn phim', _currentStep.value >= 0),
                        _buildStepConnector(_currentStep.value >= 1),
                        _buildStepIndicator(
                            1, 'Chọn rạp', _currentStep.value >= 1),
                        _buildStepConnector(_currentStep.value >= 2),
                        _buildStepIndicator(
                            2, 'Chọn lịch', _currentStep.value >= 2),
                        _buildStepConnector(_currentStep.value >= 3),
                        _buildStepIndicator(
                            3, 'Xác nhận', _currentStep.value >= 3),
                      ],
                    )),
              ),

              const SizedBox(height: 20),

              // Content
              Expanded(
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) => _currentStep.value = index,
                  children: [
                    _buildMovieSelectionStep(),
                    _buildTheaterSelectionStep(),
                    _buildScheduleSelectionStep(),
                    _buildConfirmationStep(),
                  ],
                ),
              ),

              // Navigation buttons
              Container(
                padding: const EdgeInsets.all(16),
                child: Obx(() => Row(
                      children: [
                        if (_currentStep.value > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _previousStep,
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.white,
                                side: const BorderSide(color: Colors.white),
                              ),
                              child: Text(
                                'Quay lại',
                                style: GoogleFonts.mulish(),
                              ),
                            ),
                          ),
                        if (_currentStep.value > 0) const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _canProceed() ? _nextStep : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              _getNextButtonText(),
                              style: GoogleFonts.mulish(
                                  fontWeight: FontWeight.bold),
                            ),
                          ),
                        ),
                      ],
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label, bool isActive) {
    return Column(
      children: [
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: isActive ? Colors.blue : Colors.white.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: GoogleFonts.mulish(
                color: isActive ? Colors.white : Colors.white.withOpacity(0.7),
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: GoogleFonts.mulish(
            color: Colors.white.withOpacity(0.8),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildStepConnector(bool isActive) {
    return Expanded(
      child: Container(
        height: 2,
        margin: const EdgeInsets.only(bottom: 20),
        color: isActive ? Colors.blue : Colors.white.withOpacity(0.3),
      ),
    );
  }

  Widget _buildMovieSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn phim cần lập lịch chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              if (_scheduleController.movies.isEmpty) {
                return Center(
                  child: Text(
                    'Không có phim nào',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                    ),
                  ),
                );
              }

              return ListView.builder(
                itemCount: _scheduleController.movies.length,
                itemBuilder: (context, index) {
                  final movie = _scheduleController.movies[index];
                  return _buildMovieCard(movie);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildMovieCard(Movie movie) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedMovie.value?.id == movie.id;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ListTile(
          leading: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              movie.fullPosterPath,
              width: 50,
              height: 75,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 50,
                  height: 75,
                  color: Colors.grey[800],
                  child: const Icon(Icons.movie, color: Colors.white54),
                );
              },
            ),
          ),
          title: Text(
            movie.title,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (movie.runtime != null)
                Text(
                  '${movie.runtime} phút',
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
              if (movie.genres.isNotEmpty)
                Text(
                  movie.genres.take(2).join(', '),
                  style: GoogleFonts.mulish(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 12,
                  ),
                ),
            ],
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.blue)
              : null,
          onTap: () => _scheduleController.setSelectedMovie(movie),
        ),
      );
    });
  }

  Widget _buildTheaterSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn rạp chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (_scheduleController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                );
              }

              return ListView.builder(
                itemCount: _scheduleController.theaters.length,
                itemBuilder: (context, index) {
                  final theater = _scheduleController.theaters[index];
                  return _buildTheaterCard(theater);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTheaterCard(TheaterModel theater) {
    return Obx(() {
      final isSelected =
          _scheduleController.selectedTheater.value?.id == theater.id;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Colors.blue.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white.withOpacity(0.2),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: ExpansionTile(
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.local_movies, color: Colors.blue),
          ),
          title: Text(
            theater.name,
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Text(
            theater.address.fullAddress,
            style: GoogleFonts.mulish(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
            ),
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: Colors.blue)
              : const Icon(Icons.expand_more, color: Colors.white),
          onExpansionChanged: (expanded) {
            if (expanded) {
              _scheduleController.setSelectedTheater(theater);
            }
          },
          children: [
            if (isSelected) _buildScreenSelection(),
          ],
        ),
      );
    });
  }

  Widget _buildScreenSelection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn phòng chiếu:',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Obx(() => Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _scheduleController.screens.map((screen) {
                  final isSelected =
                      _scheduleController.selectedScreenIds.contains(screen.id);
                  return GestureDetector(
                    onTap: () =>
                        _scheduleController.toggleScreenSelection(screen.id),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.blue
                            : Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: isSelected
                              ? Colors.blue
                              : Colors.white.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        screen.displayName,
                        style: GoogleFonts.mulish(
                          color: isSelected
                              ? Colors.white
                              : Colors.white.withOpacity(0.8),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              )),
        ],
      ),
    );
  }

  Widget _buildScheduleSelectionStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn ngày và giờ chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDateSelection(),
                  const SizedBox(height: 24),
                  _buildTimeSelection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn ngày chiếu:',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 30, // Next 30 days
            itemBuilder: (context, index) {
              final date = DateTime.now().add(Duration(days: index));
              final dateString = _formatDate(date);

              return Obx(() {
                final isSelected =
                    _scheduleController.selectedDates.contains(dateString);

                return GestureDetector(
                  onTap: () {
                    if (isSelected) {
                      _scheduleController.removeSelectedDate(dateString);
                    } else {
                      _scheduleController.addSelectedDate(date);
                    }
                  },
                  child: Container(
                    width: 70,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.blue
                          : Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? Colors.blue
                            : Colors.white.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _getDayName(date),
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${date.day}',
                          style: GoogleFonts.mulish(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _getMonthName(date),
                          style: GoogleFonts.mulish(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTimeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn giờ chiếu:',
          style: GoogleFonts.mulish(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        FutureBuilder<List<String>>(
          future: _scheduleController.getPopularTimeSlots(),
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const CircularProgressIndicator(color: Colors.white);
            }

            return Obx(() => Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: snapshot.data!.map((time) {
                    final isSelected =
                        _scheduleController.selectedTimes.contains(time);

                    return GestureDetector(
                      onTap: () {
                        if (isSelected) {
                          _scheduleController.removeSelectedTime(time);
                        } else {
                          _scheduleController.addSelectedTime(time);
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.blue
                              : Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? Colors.blue
                                : Colors.white.withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          time,
                          style: GoogleFonts.mulish(
                            color: isSelected
                                ? Colors.white
                                : Colors.white.withOpacity(0.8),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ));
          },
        ),
      ],
    );
  }

  Widget _buildConfirmationStep() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Xác nhận lịch chiếu',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildSummaryCard(),
                  const SizedBox(height: 16),
                  _buildConflictCheck(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tóm tắt lịch chiếu',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Obx(() => Text(
                _scheduleController.selectionSummary,
                style: GoogleFonts.mulish(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildConflictCheck() {
    return Obx(() {
      if (_scheduleController.conflicts.isEmpty) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withOpacity(0.5)),
          ),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Không có xung đột lịch chiếu',
                  style: GoogleFonts.mulish(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      }

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.withOpacity(0.5)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.red),
                const SizedBox(width: 12),
                Text(
                  'Có ${_scheduleController.conflicts.length} xung đột',
                  style: GoogleFonts.mulish(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ...(_scheduleController.conflicts.take(3).map((conflict) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• ${conflict.message}',
                    style: GoogleFonts.mulish(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 12,
                    ),
                  ),
                ))),
            if (_scheduleController.suggestedTimes.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Giờ chiếu được đề xuất:',
                style: GoogleFonts.mulish(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                children: _scheduleController.suggestedTimes
                    .take(5)
                    .map((time) => Chip(
                          label: Text(time),
                          backgroundColor: Colors.blue.withOpacity(0.3),
                          labelStyle: GoogleFonts.mulish(
                              color: Colors.white, fontSize: 10),
                        ))
                    .toList(),
              ),
            ],
          ],
        ),
      );
    });
  }

  // Navigation methods
  void _nextStep() {
    if (_currentStep.value < 3) {
      if (_currentStep.value == 2) {
        // Check conflicts before going to confirmation
        _scheduleController.checkScheduleConflicts();
      }
      _currentStep.value++;
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Create schedule
      _scheduleController.createSchedule().then((success) {
        if (success) {
          Get.back();
        }
      });
    }
  }

  void _previousStep() {
    if (_currentStep.value > 0) {
      _currentStep.value--;
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _canProceed() {
    switch (_currentStep.value) {
      case 0:
        return _scheduleController.selectedMovie.value != null;
      case 1:
        return _scheduleController.selectedTheater.value != null &&
            _scheduleController.selectedScreenIds.isNotEmpty;
      case 2:
        return _scheduleController.selectedDates.isNotEmpty &&
            _scheduleController.selectedTimes.isNotEmpty;
      case 3:
        return _scheduleController.canCreateSchedule;
      default:
        return false;
    }
  }

  String _getNextButtonText() {
    switch (_currentStep.value) {
      case 0:
        return 'Chọn rạp';
      case 1:
        return 'Chọn lịch';
      case 2:
        return 'Kiểm tra';
      case 3:
        return 'Tạo lịch chiếu';
      default:
        return 'Tiếp tục';
    }
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _getDayName(DateTime date) {
    const days = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
    return days[date.weekday % 7];
  }

  String _getMonthName(DateTime date) {
    const months = [
      'T1',
      'T2',
      'T3',
      'T4',
      'T5',
      'T6',
      'T7',
      'T8',
      'T9',
      'T10',
      'T11',
      'T12'
    ];
    return months[date.month - 1];
  }
}
