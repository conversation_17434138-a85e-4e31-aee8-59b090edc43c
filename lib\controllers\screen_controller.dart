import 'package:get/get.dart' hide ScreenType;
import '../models/screen_model.dart';
import '../models/theater_model.dart';
import '../services/screen_service.dart';
import '../services/theater_service.dart';

class ScreenController extends GetxController {
  final ScreenService _screenService = ScreenService();
  final TheaterService _theaterService = TheaterService();

  // Observable variables
  final RxList<ScreenModel> screens = <ScreenModel>[].obs;
  final RxList<ScreenModel> filteredScreens = <ScreenModel>[].obs;
  final RxList<TheaterModel> theaters = <TheaterModel>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;

  // Selected values
  final Rx<TheaterModel?> selectedTheater = Rx<TheaterModel?>(null);
  final Rx<ScreenModel?> selectedScreen = Rx<ScreenModel?>(null);

  // Filter options
  final Rx<ScreenType?> selectedType = Rx<ScreenType?>(null);
  final RxBool showActiveOnly = true.obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();

    // Listen to search query changes
    debounce(searchQuery, (_) => _filterScreens(),
        time: const Duration(milliseconds: 500));
  }

  Future<void> loadInitialData() async {
    await Future.wait([
      loadTheaters(),
      loadAllScreens(),
    ]);
  }

  Future<void> loadTheaters() async {
    try {
      isLoading.value = true;
      final theaterList = await _theaterService.getAllTheaters();
      theaters.value = theaterList;
    } catch (e) {
      errorMessage.value = 'Không thể tải danh sách rạp: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadAllScreens() async {
    try {
      isLoading.value = true;
      final screenList = await _screenService.getAllScreens(activeOnly: false);
      screens.value = screenList;
      _filterScreens();
    } catch (e) {
      errorMessage.value = 'Không thể tải danh sách phòng chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadScreensByTheater(String theaterId) async {
    try {
      isLoading.value = true;
      final screenList = await _screenService.getScreensByTheater(theaterId,
          activeOnly: false);
      screens.value = screenList;
      _filterScreens();
    } catch (e) {
      errorMessage.value = 'Không thể tải danh sách phòng chiếu: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // CRUD Operations
  Future<bool> createScreen(ScreenModel screen) async {
    try {
      isLoading.value = true;
      final newScreen = await _screenService.addScreen(screen);
      screens.add(newScreen);
      _filterScreens();

      Get.snackbar(
        'Thành công',
        'Đã tạo phòng chiếu "${screen.name}" thành công',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Không thể tạo phòng chiếu: $e';
      Get.snackbar(
        'Lỗi',
        'Không thể tạo phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> updateScreen(ScreenModel screen) async {
    try {
      isLoading.value = true;
      await _screenService.updateScreen(screen);

      // Update local list
      final index = screens.indexWhere((s) => s.id == screen.id);
      if (index != -1) {
        screens[index] = screen;
        _filterScreens();
      }

      Get.snackbar(
        'Thành công',
        'Đã cập nhật phòng chiếu "${screen.name}" thành công',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Không thể cập nhật phòng chiếu: $e';
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> toggleScreenStatus(ScreenModel screen) async {
    try {
      isLoading.value = true;
      final updatedScreen = screen.copyWith(
        isActive: !screen.isActive,
        updatedAt: DateTime.now(),
      );

      await _screenService.updateScreen(updatedScreen);

      // Update local list
      final index = screens.indexWhere((s) => s.id == screen.id);
      if (index != -1) {
        screens[index] = updatedScreen;
        _filterScreens();
      }

      final status = updatedScreen.isActive ? 'kích hoạt' : 'tạm dừng';
      Get.snackbar(
        'Thành công',
        'Đã $status phòng chiếu "${screen.name}"',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Không thể thay đổi trạng thái phòng chiếu: $e';
      Get.snackbar(
        'Lỗi',
        'Không thể thay đổi trạng thái phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> deleteScreen(ScreenModel screen) async {
    try {
      isLoading.value = true;
      await _screenService.deleteScreen(screen.id);

      // Remove from local list
      screens.removeWhere((s) => s.id == screen.id);
      _filterScreens();

      Get.snackbar(
        'Thành công',
        'Đã xóa phòng chiếu "${screen.name}" thành công',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.primaryColor,
        colorText: Get.theme.colorScheme.onPrimary,
      );

      return true;
    } catch (e) {
      errorMessage.value = 'Không thể xóa phòng chiếu: $e';
      Get.snackbar(
        'Lỗi',
        'Không thể xóa phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error,
        colorText: Get.theme.colorScheme.onError,
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Search and Filter
  void setSearchQuery(String query) {
    searchQuery.value = query;
  }

  void setSelectedTheater(TheaterModel? theater) {
    selectedTheater.value = theater;
    if (theater != null) {
      loadScreensByTheater(theater.id);
    } else {
      loadAllScreens();
    }
  }

  void setSelectedType(ScreenType? type) {
    selectedType.value = type;
    _filterScreens();
  }

  void toggleShowActiveOnly() {
    showActiveOnly.value = !showActiveOnly.value;
    _filterScreens();
  }

  void clearFilters() {
    searchQuery.value = '';
    selectedTheater.value = null;
    selectedType.value = null;
    showActiveOnly.value = true;
    loadAllScreens();
  }

  void _filterScreens() {
    var filtered = screens.toList();

    // Filter by search query
    if (searchQuery.value.isNotEmpty) {
      final query = searchQuery.value.toLowerCase();
      filtered = filtered.where((screen) {
        return screen.name.toLowerCase().contains(query) ||
            screen.type.displayName.toLowerCase().contains(query) ||
            _getTheaterName(screen.theaterId).toLowerCase().contains(query);
      }).toList();
    }

    // Filter by type
    if (selectedType.value != null) {
      filtered = filtered
          .where((screen) => screen.type == selectedType.value)
          .toList();
    }

    // Filter by active status
    if (showActiveOnly.value) {
      filtered = filtered.where((screen) => screen.isActive).toList();
    }

    filteredScreens.value = filtered;
  }

  // Helper methods
  String _getTheaterName(String theaterId) {
    final theater = theaters.firstWhereOrNull((t) => t.id == theaterId);
    return theater?.name ?? 'Unknown Theater';
  }

  TheaterModel? getTheaterById(String theaterId) {
    return theaters.firstWhereOrNull((t) => t.id == theaterId);
  }

  // Statistics
  Map<String, int> get screenStats {
    final total = screens.length;
    final active = screens.where((s) => s.isActive).length;
    final inactive = total - active;

    final byType = <String, int>{};
    for (final type in ScreenType.values) {
      byType[type.displayName] = screens.where((s) => s.type == type).length;
    }

    return {
      'total': total,
      'active': active,
      'inactive': inactive,
      ...byType,
    };
  }

  // Validation
  bool validateScreenData({
    required String name,
    required String theaterId,
    required ScreenType type,
    required int rows,
    required int seatsPerRow,
    String? screenId,
  }) {
    // Check if name is empty
    if (name.trim().isEmpty) {
      Get.snackbar('Lỗi', 'Tên phòng chiếu không được để trống');
      return false;
    }

    // Check if theater is selected
    if (theaterId.isEmpty) {
      Get.snackbar('Lỗi', 'Vui lòng chọn rạp chiếu');
      return false;
    }

    // Check if rows and seats are valid
    if (rows <= 0 || seatsPerRow <= 0) {
      Get.snackbar('Lỗi', 'Số hàng ghế và số ghế mỗi hàng phải lớn hơn 0');
      return false;
    }

    // Check for duplicate name in same theater
    final existingScreen = screens.firstWhereOrNull((s) =>
        s.theaterId == theaterId &&
        s.name.toLowerCase() == name.toLowerCase() &&
        s.id != screenId);

    if (existingScreen != null) {
      Get.snackbar('Lỗi', 'Tên phòng chiếu đã tồn tại trong rạp này');
      return false;
    }

    return true;
  }

  // Refresh data
  Future<void> refreshData() async {
    await loadInitialData();
  }
}
