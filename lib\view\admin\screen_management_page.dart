import 'package:flutter/material.dart';
import 'package:get/get.dart' hide ScreenType;
import 'package:google_fonts/google_fonts.dart';
import '../../models/screen_model.dart';
import '../../services/screen_service.dart';

class ScreenManagementPage extends StatefulWidget {
  final String theaterId;

  const ScreenManagementPage({Key? key, required this.theaterId})
      : super(key: key);

  @override
  State<ScreenManagementPage> createState() => _ScreenManagementPageState();
}

class _ScreenManagementPageState extends State<ScreenManagementPage> {
  final ScreenService _screenService = ScreenService();
  final RxList<ScreenModel> _screens = <ScreenModel>[].obs;
  final RxList<ScreenModel> _filteredScreens = <ScreenModel>[].obs;
  final RxBool _isLoading = false.obs;
  final TextEditingController _searchController = TextEditingController();
  final Rx<ScreenType?> _selectedType = Rx<ScreenType?>(null);

  @override
  void initState() {
    super.initState();
    _loadScreens();
  }

  Future<void> _loadScreens() async {
    try {
      _isLoading.value = true;
      final screens =
          await _screenService.getScreensByTheater(widget.theaterId);
      _screens.value = screens;
      _filteredScreens.value = screens;
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách phòng chiếu: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }

  void _filterScreens() {
    final query = _searchController.text.toLowerCase();
    final selectedType = _selectedType.value;

    _filteredScreens.value = _screens.where((screen) {
      final matchesSearch = screen.name.toLowerCase().contains(query) ||
          screen.type.displayName.toLowerCase().contains(query);
      final matchesType = selectedType == null || screen.type == selectedType;

      return matchesSearch && matchesType;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Quản Lý Phòng Chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        // TODO: Implement AddScreenPage
                        Get.snackbar(
                            'Thông báo', 'Tính năng sẽ được cập nhật sớm');
                      },
                      icon: const Icon(Icons.add, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Search and Filter
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // Search Bar
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextField(
                        controller: _searchController,
                        onChanged: (_) => _filterScreens(),
                        style: const TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          hintText: 'Tìm kiếm phòng chiếu...',
                          hintStyle:
                              TextStyle(color: Colors.white.withOpacity(0.7)),
                          prefixIcon: Icon(Icons.search,
                              color: Colors.white.withOpacity(0.7)),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(16),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Type Filter
                    Obx(() => Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: DropdownButtonHideUnderline(
                            child: DropdownButton<ScreenType?>(
                              value: _selectedType.value,
                              hint: Text(
                                'Chọn loại phòng',
                                style: TextStyle(
                                    color: Colors.white.withOpacity(0.7)),
                              ),
                              dropdownColor: const Color(0xff2B5876),
                              style: const TextStyle(color: Colors.white),
                              items: [
                                DropdownMenuItem<ScreenType?>(
                                  value: null,
                                  child: Text(
                                    'Tất cả loại phòng',
                                    style:
                                        GoogleFonts.mulish(color: Colors.white),
                                  ),
                                ),
                                ...ScreenType.values.map(
                                    (type) => DropdownMenuItem<ScreenType?>(
                                          value: type,
                                          child: Text(
                                            type.displayName,
                                            style: GoogleFonts.mulish(
                                                color: Colors.white),
                                          ),
                                        )),
                              ],
                              onChanged: (value) {
                                _selectedType.value = value;
                                _filterScreens();
                              },
                            ),
                          ),
                        )),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Screens List
              Expanded(
                child: Obx(() {
                  if (_isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    );
                  }

                  if (_filteredScreens.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.tv_outlined,
                            size: 64,
                            color: Colors.white.withOpacity(0.5),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Không có phòng chiếu nào',
                            style: GoogleFonts.mulish(
                              fontSize: 18,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: _loadScreens,
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredScreens.length,
                      itemBuilder: (context, index) {
                        final screen = _filteredScreens[index];
                        return _buildScreenCard(screen);
                      },
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScreenCard(ScreenModel screen) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                _getTypeColor(screen.type).withOpacity(0.3),
                _getTypeColor(screen.type).withOpacity(0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            _getTypeIcon(screen.type),
            color: _getTypeColor(screen.type),
            size: 24,
          ),
        ),
        title: Text(
          screen.name,
          style: GoogleFonts.mulish(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getTypeColor(screen.type).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                        color: _getTypeColor(screen.type).withOpacity(0.5)),
                  ),
                  child: Text(
                    screen.type.displayName,
                    style: GoogleFonts.mulish(
                      fontSize: 10,
                      color: _getTypeColor(screen.type),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${screen.totalSeats} ghế',
                  style: GoogleFonts.mulish(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (screen.amenities.isNotEmpty)
              Wrap(
                spacing: 4,
                children: screen.amenities
                    .take(2)
                    .map(
                      (amenity) => Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.blue.withOpacity(0.5)),
                        ),
                        child: Text(
                          _getAmenityDisplayName(amenity),
                          style: GoogleFonts.mulish(
                            fontSize: 10,
                            color: Colors.blue[200],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: screen.isActive
                    ? Colors.green.withOpacity(0.2)
                    : Colors.red.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: screen.isActive ? Colors.green : Colors.red,
                ),
              ),
              child: Text(
                screen.isActive ? 'Hoạt động' : 'Tạm dừng',
                style: GoogleFonts.mulish(
                  fontSize: 10,
                  color: screen.isActive ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16),
          ],
        ),
        onTap: () {
          // TODO: Implement ScreenDetailPage
          Get.snackbar('Thông báo', 'Tính năng sẽ được cập nhật sớm');
        },
      ),
    );
  }

  Color _getTypeColor(ScreenType type) {
    switch (type) {
      case ScreenType.standard:
        return Colors.grey;
      case ScreenType.vip:
        return Colors.amber;
      case ScreenType.imax:
        return Colors.red;
      case ScreenType.threeDimensional:
        return Colors.blue;
      case ScreenType.fourDX:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(ScreenType type) {
    switch (type) {
      case ScreenType.standard:
        return Icons.tv;
      case ScreenType.vip:
        return Icons.star;
      case ScreenType.imax:
        return Icons.movie;
      case ScreenType.threeDimensional:
        return Icons.view_in_ar;
      case ScreenType.fourDX:
        return Icons.vibration;
      default:
        return Icons.tv;
    }
  }

  String _getAmenityDisplayName(String amenity) {
    switch (amenity) {
      case 'air_conditioning':
        return 'Điều hòa';
      case 'dolby_atmos':
        return 'Dolby Atmos';
      case 'reclining_seats':
        return 'Ghế nằm';
      default:
        return amenity;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
